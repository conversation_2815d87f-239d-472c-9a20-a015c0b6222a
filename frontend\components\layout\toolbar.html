<!-- 顶部工具栏组件 -->
<div class="toolbar d-flex align-items-center gap-2 p-3 bg-white border-bottom">
  <div class="input-group" style="width: 240px;">
    <span class="input-group-text">
      <i class="bi bi-search"></i>
    </span>
    <input type="text"
           class="form-control"
           id="searchKey"
           placeholder="搜索文件"
           onkeyup="handleSearch(this.value)">
  </div>

  <div class="d-flex gap-2">
    <input type="date"
           class="form-control"
           id="startDate"
           style="width: 150px;"
           placeholder="开始日期">
    <span class="align-self-center">至</span>
    <input type="date"
           class="form-control"
           id="endDate"
           style="width: 150px;"
           placeholder="结束日期">
  </div>

  <!-- 知识库下拉选择框 -->
  <select class="form-select"
          id="selectedLibrary"
          style="width: 160px;"
          onchange="handleLibraryChange(this.value)">
    <option value="">选择知识库</option>
    <!-- 选项将通过JavaScript动态添加 -->
  </select>

  <select class="form-select"
          id="filterStatus"
          style="width: 120px;"
          onchange="handleStatusChange(this.value)">
    <option value="">是否审核</option>
    <option value="0">未审核</option>
    <option value="1">已审核</option>
  </select>

  <button type="button"
          class="btn btn-primary"
          onclick="handleQuery()">
    <i class="bi bi-search me-1"></i>
    查询
  </button>

  <button type="button"
          class="btn btn-primary"
          onclick="handleOpenImport()">
    <i class="bi bi-upload me-1"></i>
    导入数据
  </button>

  <!-- 登录按钮 -->
  <div class="ms-auto">
    <button type="button"
            class="btn btn-outline-secondary login-btn"
            id="loginBtn"
            onclick="handleLoginClick('logon')">
      <i class="bi bi-person me-1"></i>
      登录
    </button>
  </div>
</div>
