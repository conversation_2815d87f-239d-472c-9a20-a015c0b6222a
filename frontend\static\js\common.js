/**
 * 知识库管理系统 - 公共JavaScript函数
 */

// ==================== 全局配置 ====================
// 初始化Bootstrap组件

// ==================== 公共工具函数 ====================

/**
 * 格式化文件大小
 * @param {number} bytes 字节数
 * @returns {string} 格式化后的大小
 */
function formatFileSize(bytes) {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 格式化时间
 * @param {string|Date} time 时间
 * @returns {string} 格式化后的时间
 */
function formatTime(time) {
  if (!time) return '';
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
}

/**
 * 截断文本
 * @param {string} str 原始文本
 * @param {number} length 最大长度
 * @returns {string} 截断后的文本
 */
function truncateText(str, length = 20) {
  if (!str) return '';
  return str.length > length ? str.slice(0, length) + '...' : str;
}

/**
 * 显示成功消息
 * @param {string} message 消息内容
 */
function showSuccess(message) {
  showAlert(message, 'success');
}

/**
 * 显示错误消息
 * @param {string} message 消息内容
 */
function showError(message) {
  showAlert(message, 'danger');
}

/**
 * 显示警告消息
 * @param {string} message 消息内容
 */
function showWarning(message) {
  showAlert(message, 'warning');
}

/**
 * 显示信息消息
 * @param {string} message 消息内容
 */
function showInfo(message) {
  showAlert(message, 'info');
}

/**
 * 显示Bootstrap警告框
 * @param {string} message 消息内容
 * @param {string} type 类型 (success, danger, warning, info)
 */
function showAlert(message, type = 'info') {
  const alertDiv = document.createElement('div');
  alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
  alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
  alertDiv.innerHTML = `
    ${message}
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
  `;

  document.body.appendChild(alertDiv);

  // 自动关闭
  setTimeout(() => {
    if (alertDiv.parentNode) {
      alertDiv.remove();
    }
  }, 5000);
}

/**
 * 确认对话框
 * @param {string} message 确认消息
 * @param {string} title 标题
 * @returns {Promise} Promise对象
 */
function confirmDialog(message, title = '确认') {
  return new Promise((resolve, reject) => {
    const modalId = 'confirmModal_' + Date.now();
    const modalHtml = `
      <div class="modal fade" id="${modalId}" tabindex="-1">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">${title}</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
              <p>${message}</p>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
              <button type="button" class="btn btn-primary" id="${modalId}_confirm">确定</button>
            </div>
          </div>
        </div>
      </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = document.getElementById(modalId);
    const confirmBtn = document.getElementById(modalId + '_confirm');

    const modalInstance = new Bootstrap.Modal(modal);
    modalInstance.show();

    confirmBtn.addEventListener('click', () => {
      modalInstance.hide();
      modal.remove();
      resolve();
    });

    modal.addEventListener('hidden.bs.modal', () => {
      if (modal.parentNode) {
        modal.remove();
        reject();
      }
    });
  });
}

/**
 * 加载状态管理
 */
const LoadingManager = {
  instance: null,

  show(text = '加载中...') {
    if (this.instance) {
      this.hide();
    }

    const loadingHtml = `
      <div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
          <div class="modal-content border-0 bg-transparent">
            <div class="modal-body text-center">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
              <div class="mt-3 text-white">${text}</div>
            </div>
          </div>
        </div>
      </div>
    `;

    document.body.insertAdjacentHTML('beforeend', loadingHtml);
    this.instance = document.getElementById('loadingModal');
    const modalInstance = new Bootstrap.Modal(this.instance);
    modalInstance.show();
  },

  hide() {
    if (this.instance) {
      const modalInstance = Bootstrap.Modal.getInstance(this.instance);
      if (modalInstance) {
        modalInstance.hide();
      }
      this.instance.remove();
      this.instance = null;
    }
  }
};

/**
 * HTTP请求封装
 */
const ApiClient = {
  /**
   * GET请求
   * @param {string} url 请求URL
   * @param {object} params 请求参数
   * @returns {Promise} Promise对象
   */
  async get(url, params = {}) {
    try {
      const response = await axios.get(url, { params });
      return response.data;
    } catch (error) {
      console.error('GET请求失败:', error);
      showError('请求失败: ' + (error.response?.data?.detail || error.message));
      throw error;
    }
  },

  /**
   * POST请求
   * @param {string} url 请求URL
   * @param {object} data 请求数据
   * @returns {Promise} Promise对象
   */
  async post(url, data = {}) {
    try {
      const response = await axios.post(url, data);
      return response.data;
    } catch (error) {
      console.error('POST请求失败:', error);
      showError('请求失败: ' + (error.response?.data?.detail || error.message));
      throw error;
    }
  },

  /**
   * 文件上传
   * @param {string} url 上传URL
   * @param {FormData} formData 表单数据
   * @param {function} onProgress 进度回调
   * @returns {Promise} Promise对象
   */
  async upload(url, formData, onProgress = null) {
    try {
      const config = {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      };
      
      if (onProgress) {
        config.onUploadProgress = (progressEvent) => {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(percentCompleted);
        };
      }
      
      const response = await axios.post(url, formData, config);
      return response.data;
    } catch (error) {
      console.error('文件上传失败:', error);
      showError('上传失败: ' + (error.response?.data?.detail || error.message));
      throw error;
    }
  }
};

/**
 * 本地存储管理
 */
const StorageManager = {
  /**
   * 设置本地存储
   * @param {string} key 键
   * @param {any} value 值
   */
  set(key, value) {
    try {
      localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error('设置本地存储失败:', error);
    }
  },

  /**
   * 获取本地存储
   * @param {string} key 键
   * @param {any} defaultValue 默认值
   * @returns {any} 存储的值
   */
  get(key, defaultValue = null) {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
      console.error('获取本地存储失败:', error);
      return defaultValue;
    }
  },

  /**
   * 删除本地存储
   * @param {string} key 键
   */
  remove(key) {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.error('删除本地存储失败:', error);
    }
  },

  /**
   * 清空本地存储
   */
  clear() {
    try {
      localStorage.clear();
    } catch (error) {
      console.error('清空本地存储失败:', error);
    }
  }
};

/**
 * 表单验证规则
 */
const ValidationRules = {
  required: { required: true, message: '此项为必填项', trigger: 'blur' },
  email: { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' },
  phone: { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' },
  
  /**
   * 自定义长度验证
   * @param {number} min 最小长度
   * @param {number} max 最大长度
   * @returns {object} 验证规则
   */
  length(min, max) {
    return { min, max, message: `长度在 ${min} 到 ${max} 个字符`, trigger: 'blur' };
  },

  /**
   * 自定义数字范围验证
   * @param {number} min 最小值
   * @param {number} max 最大值
   * @returns {object} 验证规则
   */
  numberRange(min, max) {
    return { 
      type: 'number', 
      min, 
      max, 
      message: `请输入 ${min} 到 ${max} 之间的数字`, 
      trigger: 'blur' 
    };
  }
};

// ==================== 全局工具函数 ====================
// 将工具函数挂载到全局对象
window.formatFileSize = formatFileSize;
window.formatTime = formatTime;
window.truncateText = truncateText;
window.showSuccess = showSuccess;
window.showError = showError;
window.showWarning = showWarning;
window.showInfo = showInfo;
window.confirmDialog = confirmDialog;
window.LoadingManager = LoadingManager;
window.ApiClient = ApiClient;
window.StorageManager = StorageManager;
window.ValidationRules = ValidationRules;

// 导出到全局
window.CommonUtils = {
  formatFileSize,
  formatTime,
  truncateText,
  showSuccess,
  showError,
  showWarning,
  showInfo,
  confirmDialog,
  LoadingManager,
  ApiClient,
  StorageManager,
  ValidationRules
};

// ==================== DOM操作工具 ====================
/**
 * 简化的DOM操作工具
 */
window.DOMUtils = {
  /**
   * 根据ID获取元素
   */
  byId: (id) => document.getElementById(id),

  /**
   * 根据选择器获取元素
   */
  query: (selector) => document.querySelector(selector),

  /**
   * 根据选择器获取所有元素
   */
  queryAll: (selector) => document.querySelectorAll(selector),

  /**
   * 创建元素
   */
  create: (tag, attrs = {}, content = '') => {
    const el = document.createElement(tag);
    Object.keys(attrs).forEach(key => {
      if (key === 'className') {
        el.className = attrs[key];
      } else {
        el.setAttribute(key, attrs[key]);
      }
    });
    if (content) {
      el.innerHTML = content;
    }
    return el;
  },

  /**
   * 显示元素
   */
  show: (el) => {
    if (typeof el === 'string') el = document.querySelector(el);
    if (el) el.style.display = '';
  },

  /**
   * 隐藏元素
   */
  hide: (el) => {
    if (typeof el === 'string') el = document.querySelector(el);
    if (el) el.style.display = 'none';
  },

  /**
   * 切换元素显示状态
   */
  toggle: (el) => {
    if (typeof el === 'string') el = document.querySelector(el);
    if (el) {
      el.style.display = el.style.display === 'none' ? '' : 'none';
    }
  }
};
