/**
 * jQuery 简化版本 - 用于替代Vue.js的DOM操作
 * 包含基本的选择器、事件处理、AJAX等功能
 */

(function(global) {
  'use strict';

  function jQuery(selector) {
    return new jQuery.fn.init(selector);
  }

  jQuery.fn = jQuery.prototype = {
    constructor: jQuery,
    length: 0,

    init: function(selector) {
      if (!selector) {
        return this;
      }

      if (typeof selector === 'string') {
        const elements = document.querySelectorAll(selector);
        this.length = elements.length;
        for (let i = 0; i < elements.length; i++) {
          this[i] = elements[i];
        }
      } else if (selector.nodeType) {
        this[0] = selector;
        this.length = 1;
      }

      return this;
    },

    // DOM操作
    html: function(content) {
      if (content === undefined) {
        return this[0] ? this[0].innerHTML : '';
      }
      for (let i = 0; i < this.length; i++) {
        this[i].innerHTML = content;
      }
      return this;
    },

    text: function(content) {
      if (content === undefined) {
        return this[0] ? this[0].textContent : '';
      }
      for (let i = 0; i < this.length; i++) {
        this[i].textContent = content;
      }
      return this;
    },

    val: function(value) {
      if (value === undefined) {
        return this[0] ? this[0].value : '';
      }
      for (let i = 0; i < this.length; i++) {
        this[i].value = value;
      }
      return this;
    },

    attr: function(name, value) {
      if (value === undefined) {
        return this[0] ? this[0].getAttribute(name) : null;
      }
      for (let i = 0; i < this.length; i++) {
        this[i].setAttribute(name, value);
      }
      return this;
    },

    addClass: function(className) {
      for (let i = 0; i < this.length; i++) {
        this[i].classList.add(className);
      }
      return this;
    },

    removeClass: function(className) {
      for (let i = 0; i < this.length; i++) {
        this[i].classList.remove(className);
      }
      return this;
    },

    toggleClass: function(className) {
      for (let i = 0; i < this.length; i++) {
        this[i].classList.toggle(className);
      }
      return this;
    },

    hasClass: function(className) {
      return this[0] ? this[0].classList.contains(className) : false;
    },

    show: function() {
      for (let i = 0; i < this.length; i++) {
        this[i].style.display = '';
      }
      return this;
    },

    hide: function() {
      for (let i = 0; i < this.length; i++) {
        this[i].style.display = 'none';
      }
      return this;
    },

    // 事件处理
    on: function(event, handler) {
      for (let i = 0; i < this.length; i++) {
        this[i].addEventListener(event, handler);
      }
      return this;
    },

    off: function(event, handler) {
      for (let i = 0; i < this.length; i++) {
        this[i].removeEventListener(event, handler);
      }
      return this;
    },

    click: function(handler) {
      if (handler) {
        return this.on('click', handler);
      } else {
        for (let i = 0; i < this.length; i++) {
          this[i].click();
        }
        return this;
      }
    },

    // 查找
    find: function(selector) {
      const result = jQuery();
      for (let i = 0; i < this.length; i++) {
        const found = this[i].querySelectorAll(selector);
        for (let j = 0; j < found.length; j++) {
          result[result.length] = found[j];
          result.length++;
        }
      }
      return result;
    },

    parent: function() {
      const result = jQuery();
      for (let i = 0; i < this.length; i++) {
        if (this[i].parentNode) {
          result[result.length] = this[i].parentNode;
          result.length++;
        }
      }
      return result;
    },

    closest: function(selector) {
      const result = jQuery();
      for (let i = 0; i < this.length; i++) {
        let element = this[i];
        while (element && !element.matches(selector)) {
          element = element.parentNode;
        }
        if (element) {
          result[result.length] = element;
          result.length++;
        }
      }
      return result;
    }
  };

  jQuery.fn.init.prototype = jQuery.fn;

  // AJAX功能
  jQuery.ajax = function(options) {
    const defaults = {
      method: 'GET',
      url: '',
      data: null,
      contentType: 'application/json',
      success: function() {},
      error: function() {}
    };

    const settings = Object.assign(defaults, options);

    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();
      xhr.open(settings.method, settings.url);
      
      if (settings.contentType) {
        xhr.setRequestHeader('Content-Type', settings.contentType);
      }

      xhr.onload = function() {
        if (xhr.status >= 200 && xhr.status < 300) {
          let response;
          try {
            response = JSON.parse(xhr.responseText);
          } catch (e) {
            response = xhr.responseText;
          }
          settings.success(response);
          resolve(response);
        } else {
          settings.error(xhr);
          reject(xhr);
        }
      };

      xhr.onerror = function() {
        settings.error(xhr);
        reject(xhr);
      };

      let data = settings.data;
      if (data && typeof data === 'object' && settings.contentType === 'application/json') {
        data = JSON.stringify(data);
      }

      xhr.send(data);
    });
  };

  jQuery.get = function(url, success) {
    return jQuery.ajax({
      method: 'GET',
      url: url,
      success: success
    });
  };

  jQuery.post = function(url, data, success) {
    return jQuery.ajax({
      method: 'POST',
      url: url,
      data: data,
      success: success
    });
  };

  // 工具函数
  jQuery.each = function(obj, callback) {
    if (Array.isArray(obj)) {
      for (let i = 0; i < obj.length; i++) {
        if (callback.call(obj[i], i, obj[i]) === false) {
          break;
        }
      }
    } else {
      for (let key in obj) {
        if (obj.hasOwnProperty(key)) {
          if (callback.call(obj[key], key, obj[key]) === false) {
            break;
          }
        }
      }
    }
    return obj;
  };

  // 暴露到全局
  global.$ = global.jQuery = jQuery;

  // DOM ready
  jQuery.ready = function(callback) {
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', callback);
    } else {
      callback();
    }
  };

  // 简化的ready方法
  jQuery.fn.ready = function(callback) {
    jQuery.ready(callback);
    return this;
  };

})(window);
