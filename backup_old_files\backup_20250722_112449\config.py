from pymongo import MongoClient
from bson.objectid import ObjectId
def init_monogdb():
    client = MongoClient(f"mongodb://myusername:mypassword@{serverIP}:27017/fastgpt?authSource=admin&directConnection=true")
    db = client['fastgpt']
    return db
# 后端连接数据库使用  ***********   127.0.0.1
serverIP = "**************"
# 接口使用 
apiIP = "**************"
# 页面使用
templatesIP = "*************:8009"



# fastgpt接口使用
headers = {"Authorization": "Bearer fastgpt-e5Lf0kDHo9kXA29cgNVroVWfdNhlw2Csx3ErmS0A0ISfhJyh1EL6eP1HNEqGIX4o"}

# 默认登录账号密码
baseusername = "admin"
basepassword = "whyf2025"

# 默认查询对话知识库
db = init_monogdb()
dataids_list = [a["_id"] for a in db['datasets'].find({"parentId": ObjectId("675f86de7bb3c4679814c738")})]

zhishiku_sm_dict = {a["name"]:a["intro"] for a in db['datasets'].find({"parentId": ObjectId("675f86de7bb3c4679814c738")})}
zhishiku_ss_dict = {a["name"]:str(a["_id"]) for a in db['dataset_collections'].find({"datasetId": ObjectId("6739d0b48e3e1b37c294c883"),"type":"folder"})}

# 智普 key 和 提示词
zhipukey="d3e441ee4a536dd76c88a404ad8b05e4.t97wBPQIa5GuFT4y"
system_prompt = """
            根据这个提示词 
            Role: 知识提取专家
            Background: 用户需要从文件中提取关键知识点和关键词，并要求与文件内容相关度极高，且与文件名和内容紧密相关。
            Profile: 你是一位专注于信息提取和知识管理的专家，擅长从复杂的文件中快速识别和提炼关键知识点和关键词。
            Skills: 你具备强大的文本分析能力、信息检索技巧和高度的概括能力，能够准确把握文件的核心内容和关键词。
            Goals: 提取文件中的关键知识点和关键词，确保与文件内容的相关度极高。
            Constraints:
            提取的关键知识点和关键词必须包含产品型号。
            关键知识点和关键词必须与文件内容相关度达到99%以上。
            Output Format: 简洁的描述，关键知识点不超过15个字，关键词不超过4个。
            Workflow:
            分析文件名，确定文件的主要内容和目的（操作手册/彩页）。
            阅读文件内容，识别与文件名相关的关键信息和关键词。
            提取包含产品型号的关键信息和关键词，并确保相关度极高。
            Examples:
            文件名："产品A操作手册"
            关键知识点：产品A使用流程
            关键词：产品A,操作手册,流程,使用
            文件名："产品B设备对照表"
            关键知识点：产品B兼容设备
            关键词：产品B,设备,对照表
            文件名："产品C彩页"
            关键知识点：产品C参数说明
            关键词：产品C,彩页,参数,说明
            帮我生成` `这段内容对应的相关知识点
            请按照使用如下 JSON 格式输出你的回复：
            {"text": "关键知识点","text2":"关键词"}
        """

bs_prompt = """
            - Role: 安防行业标书信息处理专家
            - Background: 用户已将安防行业的标书文档导入知识库，需要对知识库中的标书片段进行处理，生成以JSON格式输出的摘要和索引，以便后续快速检索关键信息。用户重点关注产品参数信息，要求在生成摘要时不能遗漏任何一条软件或设备的相关要求和参数。
            - Profile: 你是一位在安防行业标书处理领域经验丰富的专家，对安防行业的标书结构、内容以及关键信息的提取有着深入的理解和丰富的实践经验，擅长从复杂的文本中精准提取关键信息并进行高效组织，同时具备将信息以JSON格式规范输出的能力。
            - Skills: 你具备强大的文本分析能力、信息提取能力、摘要生成能力和索引编制能力，能够精准识别标书中的资质、服务要求、关键信息（时间、预算等）、客户信息以及产品参数信息等关键内容，并确保在生成摘要时完整保留软件或设备的相关要求和参数，同时能够将索引和摘要以JSON格式准确输出。
            - Goals: 为知识库中的标书片段生成精准的摘要和不超过10个字的索引，并以JSON格式输出，确保在后续检索时能够快速定位到标书中的关键信息，特别是产品参数信息，且在生成摘要时绝对不能遗漏任何一条软件或设备的相关要求和参数。
            - Constrains: 生成的索引不得超过10个字，摘要必须完整包含软件或设备的相关要求和参数，确保信息的准确性和完整性，输出格式必须符合JSON规范。
            - OutputFormat: 以JSON格式输出，包含索引和摘要两个字段。
            - Workflow:
            1. 仔细阅读并理解标书片段内容，识别其中的关键信息，包括资质、服务要求、关键信息（时间、预算等）、客户信息以及产品参数信息等。
            2. 对于产品参数信息部分，逐条提取软件或设备的相关要求和参数，确保不遗漏任何一条。
            3. 根据提取的关键信息，生成精准的摘要，同时提炼出不超过10个字的索引，确保索引能够准确反映片段的核心内容，并将索引和摘要以JSON格式输出,text为索引，text2为摘要。
            - Examples:
            - 例子1：标书片段内容为“本项目要求投标方具备安防工程企业资质，服务响应时间为24小时以内，预算为100万元，客户为某大型企业。监控设备需支持高清视频采集，存储容量不低于1TB，具备智能分析功能。”
                ```json
                {
                "text": "监控设备参数",
                "text2": "投标方需具备安防工程企业资质，服务响应时间24小时以内，预算100万元，客户为某大型企业。监控设备要求：支持高清视频采集，存储容量不低于1TB，具备智能分析功能。"
                }
                ```
            - 例子2：标书片段内容为“门禁系统软件需支持多用户管理，具备人脸识别功能，系统响应时间不超过1秒。项目实施时间为2024年1月至2024年6月，预算为50万元，客户为某政府机关。”
                ```json
                {
                "text": "门禁系统软件",
                "text2": "门禁系统软件要求：支持多用户管理，具备人脸识别功能，系统响应时间不超过1秒。项目实施时间为2024年1月至2024年6月，预算50万元，客户为某政府机关。"
                }
                ```
            - 例子3：标书片段内容为“报警系统设备需具备高灵敏度探测功能，支持远程报警通知，设备功耗不超过10W。项目要求投标方具备相关资质，服务响应时间为48小时以内，预算为80万元，客户为某商业综合体。”
                ```json
                {
                "text": "报警系统设备",
                "text2": "报警系统设备要求：具备高灵敏度探测功能，支持远程报警通知，设备功耗不超过10W。投标方需具备相关资质，服务响应时间48小时以内，预算80万元，客户为某商业综合体。"
                }
                ```
        """


tishici_dict={
    "zkaccess3.5单机版门禁软件":"""
        <Context> zkaccess3.5门禁软件是一套cs架构的门禁管理软件。别称包括"3.5"、"3.5软件"、"单机版门禁"、"zkaccess3.5"、"门禁软件"、"zkaccess"。 </Context> 
        Q1: zkaccess3.5单机版门禁软件的主要功能是什么？ 
        A1: zkaccess3.5是一款基于CS架构的门禁管理软件，用于门禁系统的配置、权限管理、记录查询等操作，支持单机部署。
        Q2: zkaccess3.5有哪些常见的别称？
        A2: 别称包括"3.5"、"3.5软件"、"单机版门禁"、"zkaccess3.5"、"门禁软件"、"zkaccess"。
        Q3: zkaccess3.5软件反潜状态下出门开关可以开门吗？
        A3: 门反潜状态下出门开关可以开门，出门开关不受反潜限制。
    """,
    "ZKtime5.0单机版考勤软件":"""
        <Context> zktime5.0考勤软件是一套cs架构的考勤管理软件。别称包括"5.0"、"5.0软件"、"单机版考勤"、"zktime5.0"、"考勤软件"、"zktime"。 </Context> 
        Q1: zktime5.0软件支持哪些核心功能？ 
        A1: 提供考勤规则设置、排班管理、打卡记录统计、报表生成等功能，适用于企业考勤管理。
        Q2: zktime5.0的部署架构是什么？
        A2: 采用CS架构，支持单机版部署。
        Q3: zktime5.0软件的排班优先级是怎样的?
        A3: 临时排班优先于正常班优先于智能排班,智能排班时间段不能重合、不能跨天（不建议客户使用）。
    """,
    "ZKepos单机版消费软件":"""
        <Context> ZKepos4.0消费软件是一套cs架构的消费管理软件。别称包括"消费软件"、"单机版消费"、"epos消费"、"zkepos"、"epos"。 </Context> 
        Q1: ZKepos4.0的应用场景有哪些？ 
        A1: 主要应用于工厂、学校等场所的食堂扣款系统，支持离线和在线消费模式。
        Q2: zkepos4.0的别称是什么？
        A2: 别称包括"消费软件"、"单机版消费"、"epos消费"、"zkepos"、"epos"。
        Q3: zkepos4.0连接单机软件使用的消费机是否可以外接音响/喇叭？
        A3: 标配音频输出口:在线消费机：新 CM70(ZLM60)/CM70-BM、CM70、CM101/105；离线消费机：新 CM60(ZLM60)/CM60-BM、CM60、CM102 。 注意：USB 电源线另外供电，不要插机器底部的 USB 端口供电。
    """,
    "熵基互联业务块":"""
        <Context> 熵基互联是一套saas软件管理平台。别称包括"熵基互联"、"互联"。 </Context> 
        Q1: 熵基互联的平台架构是什么？ 
        A1: 基于SaaS架构，提供云端软件服务，支持多业务模块集成管理。
        Q2: 该平台的别称有哪些？
        A2: 别称包括"熵基互联"、"互联"。
    """,
    "考勤业务块":"""
        <Context> 考勤设备相关的参数资料和操作文档。别称包括"考勤机", "考勤设备"。 </Context> 
        Q1: 考勤业务块覆盖哪些内容？ 
        A1: 包括考勤机的技术参数、操作指南、故障排查文档等。
        Q2: X20设备支持的软件？
        A2: ZKTime5.0
    """,
    "门禁及梯控业务块":""""
        <Context> 包含门禁设备(门禁一体机、门禁控制器、门禁读头)。同时提供分公司售后联系方式、公司简介等。别称包括"门禁机"、"门禁设备"、"控制器"、"读头"、"国密卡"、"联系方式"。 </Context> 
        Q1: 门禁及梯控设备包含哪些硬件类型？ 
        A1: 包括门禁一体机、门禁控制器、门禁读头等。
        Q2: 该业务块是否提供售后支持信息？
        A2: 是，包含全国分公司联系方式及熵基科技公司简介、主营业务说明。
    """,
    "消费业务块":"""
        <Context> 消费设备相关的参数资料和操作文档，包括离线和在线消费系统。别称包括"消费机"、"消费设备"。 </Context> 
        Q1: 消费设备的典型应用场景是什么？ 
        A1: 适用于工厂、学校食堂的扣款场景，支持离线和在线消费模式。
        Q2: 消费设备的别称有哪些？
        A2: 别称包括"消费机"、"消费设备"。
        Q3: ZTHP500的消费模式有哪些？
        A3: 消费模式总共分为定值模式、商品模式、金额模式、计次模式、订餐模式五种模式，用户可 以通过在软件端修改更改消费模式。
    """,
    "车行业务块":"""
        <Context> 提供车牌一体机、车行道闸、充电桩等设备和ZKTecoParking软件的技术支持。别称包括"车行"、"车牌识别"、"道闸"、"充电桩"。 </Context> 
        Q1: ZKTecoParking软件的核心功能是什么？ 
        A1: 基于车牌识别技术，提供停车场管理、道闸控制、充电桩联动等功能。
        Q2: 车行设备包含哪些硬件？
        A2: 包括车牌一体机、车行道闸、充电桩等。
        Q3: LPR8800显示屏颜色不停变化，不能显示车牌号码，收费信息？
        A3: 1、检查LCD屏排线端口是否松动脱落；2、重新下发下加载下信息；3、对换下相机模组排查；4、排查以上三个操作后换LCD屏。
    """,
    "安检业务块":"""
        <Context> 提供安检设备及软件的技术支持，包括检查仪、检测仪、金属探测门等。别称包括"安检"、"检查仪"、"金属探测门"。 </Context> 
        Q1: 安检设备的应用场所有哪些？ 
        A1: 地铁站、飞机场、高铁站等需要安全检查的场所。
        Q2: 安检设备包含哪些类型？
        A2: 包括检查仪、检测仪、金属探测门等。
        Q3: 1.ZKX系列安检机过包不出图？
        A3: 检查软件是否有提示采集器连接错误，如软件提示采集器连接错误，先检查采集器IP，打开电脑网络和internet设置—更改网络适配器—IPv4，检查IP是否正确。滨松采集板：************，尚飞采集板：*************，亦瑞采集板：*************。
    """,
    "万傲瑞达业务块":"""
        <Context> 提供万傲瑞达V6600软件平台的技术支持，模块包括考勤、门禁、消费等。别称包括"万傲瑞达"、"V6600"、"6600软件"。 </Context> 
        Q1: 万傲瑞达V6600支持哪些业务模块？ 
        A1: 涵盖考勤、门禁、消费、会议、车行、梯控、视频等模块。
        Q2: 该平台的别称是什么？
        A2: 别称包括"万傲瑞达"、"V6600"、"6600软件"。
        Q3: 为什么通过一体机登记人员信息后，软件上没有对应的人员信息？
        A3: 设备接入后，需要将一体机设置为登记机模式，设备里的人员信息才会自动传到软件上。
    """,
    "ecopro软件":"""
        <Context> 提供ZKTime10/11考勤模块或Ecopro软件的技术支持，涵盖考勤、门禁等模块。别称包括"Ecopro"、"ZKTime10"、"ZKTime11"。 </Context> 
        Q1: E-ZKEco Pro系统如何传送人员到考勤设备？
        A1: 在E-ZKEco Pro系统中，传送人员到考勤设备的步骤如下：勾选需要传送的人员，点击【传送人员到考勤设备】进入考勤设备选择对话框，选择人员日常考勤的考勤设备，提交即可。
        Q2: Ecopro的别称有哪些？
        A2: 别称包括"Ecopro"、"pro软件"、"ZKTime10"、"ZKTime11"。
    """,
    "人行通道业务块":"""
        <Context> 提供翼闸、三辊闸、伸缩门等设备的技术支持。别称包括"人行通道"、"翼闸"、"三辊闸"。 </Context> 
        Q1: 人行通道设备包含哪些类型？ 
        A1: 包括翼闸、三辊闸、伸缩门、自动广告门、安全屏蔽门等。
        Q2: 通行方向和通行语音提示都正确，但是通行指示灯方向错了怎么处理？
        A2: 调换灯板 4P 端子上中间绿白线接线位置。
    """,
    "人证业务块":"""
        <Context> 提供访客机、身份证阅读器等设备的技术支持。别称包括"人证"、"访客机"、"身份证阅读器"。 </Context> 
        Q1: 人证核验设备的主要功能是什么？ 
        A1: 支持身份证信息读取、访客身份核验等功能。
        Q2: 人证魔方闸机终端700白名单怎么设置？
        A2: 白名单主要用于快递、外卖、保洁等比较有规律进出的人员进行管理， 白名单人员操作与员工管理操作类 似，但在系统的功能权限会部分受限，管理员可点击右上角按钮进行新增或者左滑进行编辑、删除和加入黑名 单的操作。
    """,
    "熵基云联":"""
        <Context> 提供ZKDIGIMAX L3平台及智能硬件（如数字标牌、机器人）的技术支持。别称包括"数字标牌"、"DGMAX"、"机器人"。 </Context> 
        Q1: ZKDIGIMAX L3平台的应用领域是什么？ 
        A1: 智慧新零售场景，支持智能硬件（摄像机、数字标牌、机器人）的综合管理。
        Q2: 熵基云联的别称有哪些？
        A2: 别称包括"数字标牌"、"DGMAX"、"机器人"、"云联"。
    """
}
tishici_dict["知识背景"] = tishici_dict["门禁及梯控业务块"]
