"""
聊天日志API处理器
处理聊天日志相关的HTTP请求
"""

from typing import Dict, Any
from fastapi import HTTPException, Request
from fastapi.responses import HTMLResponse

from .services import chat_logs_service
from config.templates import templates
from config.settings import settings


async def get_app_list() -> Dict[str, Any]:
    """获取应用列表API"""
    try:
        result = chat_logs_service.get_app_list()
        if result["code"] == 200:
            return result
        else:
            raise HTTPException(status_code=500, detail=result.get("message", "获取应用列表失败"))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


async def get_chat_logs(date_start: str, date_end: str, page_num: int, app_id: str) -> Dict[str, Any]:
    """获取聊天日志API"""
    try:
        result = chat_logs_service.get_chat_logs(date_start, date_end, page_num, app_id)
        if result["code"] == 200:
            return result
        else:
            raise HTTPException(status_code=500, detail=result.get("message", "获取聊天日志失败"))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


async def get_pagination_records(data: Dict[str, Any]) -> Dict[str, Any]:
    """获取分页记录API"""
    try:
        result = chat_logs_service.get_pagination_records(data)
        if result["code"] == 200:
            return result
        else:
            raise HTTPException(status_code=500, detail=result.get("message", "获取分页记录失败"))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


async def save_feedbacks(body: Dict[str, Any]) -> Dict[str, Any]:
    """保存反馈API"""
    try:
        chat_id = body.get("chatId", "")
        feedback_type = body.get("type", "")
        content = body.get("content", "")
        
        if not chat_id or not feedback_type:
            raise HTTPException(status_code=400, detail="缺少必要参数")
        
        result = chat_logs_service.save_feedbacks(chat_id, feedback_type, content)
        if result["code"] == 200:
            return result
        else:
            raise HTTPException(status_code=500, detail=result.get("message", "保存反馈失败"))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


async def h5_log_page(request: Request):
    """聊天日志页面 - 使用Jinja2模板渲染"""
    try:
        # 准备模板上下文数据
        context = {
            "request": request,
            "title": "对话日志管理",
            "server_ip": settings.TEMPLATES_IP,
            "api_ip": settings.API_IP
        }

        # 使用Jinja2模板渲染
        return templates.TemplateResponse("chat-logs.html", context)

    except Exception as e:
        print(f"✗ 聊天日志页面模板渲染失败: {e}")
        # 如果模板渲染失败，返回重定向页面
        html_content = f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <title>对话日志管理 - 错误</title>
        </head>
        <body>
            <div style="text-align: center; padding: 50px;">
                <h1>对话日志管理</h1>
                <p style="color: #F56C6C;">模板渲染失败: {e}</p>
                <p><a href="/">返回首页</a></p>
            </div>
        </body>
        </html>
        """
        return HTMLResponse(content=html_content, status_code=500)
