"""
AI客户端工具
提供智谱AI和其他AI服务的封装
"""

import json
from typing import Dict, <PERSON>, <PERSON><PERSON>
from zhipuai import <PERSON>hipuA<PERSON>
from config.settings import settings
from config.constants import SYSTEM_PROMPT, BS_PROMPT, FILE_INFO_PROMPT, PRODUCT_MODEL_PROMPT


class AIClient:
    """AI客户端类"""
    
    def __init__(self):
        self.client = ZhipuAI(api_key=settings.ZHIPU_API_KEY)
    
    def extract_indexes(self, content: str, prompt: str) -> Tuple[str, str]:
        """提取索引信息"""
        try:
            completion = self.client.chat.completions.create(
                model="glm-4-air",
                messages=[
                    {"role": "system", "content": prompt},
                    {"role": "user", "content": content}
                ],
                temperature=0.3,
                response_format={"type": "json_object"}
            )
            
            result = json.loads(completion.choices[0].message.content)
            gjzsd = result.get("text", "")
            gjc = result.get("text2", "")
            return gjzsd, gjc
        except Exception as e:
            print(f"AI提取索引发生错误: {e}")
            return "", ""
    
    def extract_file_info(self, filename: str) -> Dict[str, str]:
        """从文件名提取文件信息"""
        try:
            completion = self.client.chat.completions.create(
                model="glm-4-air",
                messages=[
                    {"role": "system", "content": FILE_INFO_PROMPT},
                    {"role": "user", "content": filename}
                ],
                temperature=0.3,
                response_format={"type": "json_object"}
            )
            
            response_text = completion.choices[0].message.content
            return json.loads(response_text)
        except Exception as e:
            print(f"AI提取文件信息发生错误: {e}")
            return {
                "型号": "未知",
                "名称": "未知", 
                "售前售后": "未知",
                "可接入软件": "未知"
            }
    
    def extract_product_model(self, filename: str) -> str:
        """从文件名提取产品型号"""
        try:
            completion = self.client.chat.completions.create(
                model="glm-4-air",
                messages=[
                    {"role": "system", "content": PRODUCT_MODEL_PROMPT},
                    {"role": "user", "content": filename}
                ],
                temperature=0.3,
                response_format={"type": "json_object"}
            )
            
            result = json.loads(completion.choices[0].message.content)
            return result.get("产品型号", "")
        except Exception as e:
            print(f"AI提取产品型号发生错误: {e}")
            return ""


# 创建全局AI客户端实例
ai_client = AIClient()
