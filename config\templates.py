"""
模板配置
配置Jinja2模板引擎
"""

from fastapi.templating import Jinja2Templates
import os

# 配置Jinja2模板引擎，支持多个模板目录
template_dirs = [
    "frontend/templates",
    "frontend/components"
]

# 确保所有模板目录存在
for template_dir in template_dirs:
    if not os.path.exists(template_dir):
        os.makedirs(template_dir, exist_ok=True)

templates = Jinja2Templates(directory=template_dirs)

# 添加自定义过滤器和函数
def format_size(size):
    """格式化文件大小"""
    if not size:
        return "0 B"

    try:
        size = int(size)
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"
    except (ValueError, TypeError):
        return "未知大小"

def format_time(timestamp):
    """格式化时间"""
    if not timestamp:
        return "未知时间"

    try:
        from datetime import datetime
        if isinstance(timestamp, str):
            # 尝试解析ISO格式时间
            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
        elif hasattr(timestamp, 'strftime'):
            dt = timestamp
        else:
            return str(timestamp)

        return dt.strftime('%Y-%m-%d %H:%M:%S')
    except Exception:
        return str(timestamp)

# 注册自定义过滤器
templates.env.filters['formatSize'] = format_size
templates.env.filters['formatTime'] = format_time
