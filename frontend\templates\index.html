{% extends "base.html" %}

{% block title %}知识库管理系统{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="/frontend/static/css/knowledge-base.css">
{% endblock %}

{% block content %}
<div class="main-container d-flex">
  <!-- 侧边栏导航 -->
  {% include 'components/layout/sidebar.html' %}

  <!-- 右侧内容 -->
  <div class="main-content flex-grow-1">
    <!-- 顶部工具栏 -->
    {% include 'components/layout/toolbar.html' %}

    <!-- 卡片展示区 -->
    <div class="card-box p-3">
      <div class="row g-3" id="filesContainer">
        <!-- 文件卡片将通过JavaScript动态生成 -->
      </div>

      <!-- 空状态 -->
      <div id="emptyState" class="text-center py-5 d-none">
        <i class="bi bi-folder2-open text-muted" style="font-size: 64px;"></i>
        <p class="text-muted mt-3">暂无数据</p>
      </div>

      <!-- 加载状态 -->
      <div id="loadingState" class="text-center py-5 d-none">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        <p class="text-muted mt-3">加载中...</p>
      </div>
    </div>
  </div>
</div>

<!-- 对话框组件 -->
{% include 'components/dialogs/login-dialog.html' %}
{% include 'components/dialogs/upload-dialog.html' %}
{% include 'components/dialogs/preview-dialog.html' %}

<!-- 删除确认对话框 -->
<div class="modal fade" id="deleteModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">确认删除</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <p>确定要删除文件 "<span id="deleteFileName"></span>" 吗？此操作不可撤销。</p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
        <button type="button" class="btn btn-danger" id="confirmDeleteBtn" onclick="confirmDelete()">
          <span class="spinner-border spinner-border-sm d-none me-1" id="deleteSpinner"></span>
          确定删除
        </button>
      </div>
    </div>
  </div>
</div>

<!-- 审核对话框 -->
<div class="modal fade" id="auditModal" tabindex="-1">
  <div class="modal-dialog modal-xl">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">文件审核</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div class="alert alert-info" role="alert">
          <h6 class="alert-heading">审核说明</h6>
          <p class="mb-0">请仔细检查文件内容和相同型号的文件，确认无误后进行审核。</p>
        </div>

        <h6>相同型号文件列表</h6>
        <div class="table-responsive" style="max-height: 300px;">
          <table class="table table-striped">
            <thead>
              <tr>
                <th>文件名</th>
                <th>创建时间</th>
                <th>审核状态</th>
              </tr>
            </thead>
            <tbody id="sameFilesTable">
              <!-- 数据将通过JavaScript动态填充 -->
            </tbody>
          </table>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
        <button type="button" class="btn btn-primary" id="confirmAuditBtn" onclick="confirmAudit()">
          <span class="spinner-border spinner-border-sm d-none me-1" id="auditSpinner"></span>
          通过审核
        </button>
      </div>
    </div>
  </div>
</div>

<!-- 文件信息对话框 -->
<div class="modal fade" id="fileInfoModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">文件信息</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <form id="fileInfoForm">
          <div class="mb-3">
            <label for="productModel" class="form-label">产品型号</label>
            <input type="text" class="form-control" id="productModel" name="型号">
          </div>
          <div class="mb-3">
            <label for="productName" class="form-label">产品名称</label>
            <input type="text" class="form-control" id="productName" name="名称">
          </div>
          <div class="mb-3">
            <label for="salesType" class="form-label">售前售后</label>
            <select class="form-select" id="salesType" name="售前售后">
              <option value="">请选择</option>
              <option value="售前">售前</option>
              <option value="售后">售后</option>
            </select>
          </div>
          <div class="mb-3">
            <label for="software" class="form-label">可接入软件</label>
            <input type="text" class="form-control" id="software" name="可接入软件">
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
        <button type="button" class="btn btn-primary" onclick="confirmFileInfo()">确定</button>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="/frontend/static/js/knowledge-base.js"></script>
{% endblock %}
