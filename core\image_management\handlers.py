"""
图片管理API处理器
处理图片管理相关的HTTP请求
"""

from typing import Dict, Any
from fastapi import HTTPException, Request
from fastapi.responses import HTMLResponse

from .services import image_service
from config.templates import templates
from config.settings import settings


async def get_collections_list(dataset_id: str = None) -> Dict[str, Any]:
    """获取集合列表API"""
    try:
        result = image_service.get_collections_list(dataset_id)
        if result["code"] == 200:
            return result
        else:
            raise HTTPException(status_code=500, detail=result.get("message", "获取集合列表失败"))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


async def get_datasets_datas_images(
    collection_id: str,
    date_start: str = None,
    date_end: str = None,
    offset: int = 0,
    limit: int = 10,
    is_audit: int = 0
) -> Dict[str, Any]:
    """获取数据集图片数据API"""
    try:
        result = image_service.get_datasets_datas_images(
            collection_id=collection_id,
            date_start=date_start,
            date_end=date_end,
            offset=offset,
            limit=limit,
            is_audit=is_audit
        )
        if result["code"] == 200:
            return result
        else:
            raise HTTPException(status_code=500, detail=result.get("message", "获取图片数据失败"))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


async def upload_image(body: Dict[str, Any]) -> Dict[str, Any]:
    """上传图片API"""
    try:
        result = image_service.upload_image(body)
        if result["code"] == 200:
            return result
        else:
            raise HTTPException(status_code=500, detail=result.get("message", "上传图片失败"))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


async def audit_image(collection_id: str) -> Dict[str, Any]:
    """审核图片API"""
    try:
        result = image_service.audit_image(collection_id)
        if result["code"] == 200:
            return result
        else:
            raise HTTPException(status_code=500, detail=result.get("message", "审核图片失败"))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


async def h5_images_page(request: Request):
    """图片管理页面 - 使用Jinja2模板渲染"""
    try:
        # 准备模板上下文数据
        context = {
            "request": request,
            "title": "知识库片段修改",
            "server_ip": settings.TEMPLATES_IP,
            "api_ip": settings.API_IP
        }

        # 使用Jinja2模板渲染
        return templates.TemplateResponse("image-management.html", context)

    except Exception as e:
        print(f"✗ 图片管理页面模板渲染失败: {e}")
        # 如果模板渲染失败，返回错误页面
        html_content = f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <title>知识库片段修改 - 错误</title>
        </head>
        <body>
            <div style="text-align: center; padding: 50px;">
                <h1>知识库片段修改</h1>
                <p style="color: #F56C6C;">模板渲染失败: {e}</p>
                <p><a href="/">返回首页</a></p>
            </div>
        </body>
        </html>
        """
        return HTMLResponse(content=html_content, status_code=500)


async def h5_images_audit_page(request: Request):
    """图片审核页面 - 使用Jinja2模板渲染"""
    try:
        # 准备模板上下文数据
        context = {
            "request": request,
            "title": "知识库片段审核",
            "server_ip": settings.TEMPLATES_IP,
            "api_ip": settings.API_IP
        }

        # 使用Jinja2模板渲染
        return templates.TemplateResponse("audit.html", context)

    except Exception as e:
        print(f"✗ 图片审核页面模板渲染失败: {e}")
        # 如果模板渲染失败，返回错误页面
        html_content = f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <title>知识库片段审核 - 错误</title>
        </head>
        <body>
            <div style="text-align: center; padding: 50px;">
                <h1>知识库片段审核</h1>
                <p style="color: #F56C6C;">模板渲染失败: {e}</p>
                <p><a href="/">返回首页</a></p>
            </div>
        </body>
        </html>
        """
        return HTMLResponse(content=html_content, status_code=500)
