/**
 * Bootstrap JavaScript - 简化版本
 * 包含基本的模态框、下拉菜单等功能
 */

(function() {
  'use strict';

  // 模态框功能
  class Modal {
    constructor(element) {
      this.element = element;
      this.backdrop = null;
    }

    show() {
      this.element.style.display = 'block';
      this.element.classList.add('show');
      this.showBackdrop();
      document.body.classList.add('modal-open');
    }

    hide() {
      this.element.style.display = 'none';
      this.element.classList.remove('show');
      this.hideBackdrop();
      document.body.classList.remove('modal-open');
    }

    showBackdrop() {
      if (!this.backdrop) {
        this.backdrop = document.createElement('div');
        this.backdrop.className = 'modal-backdrop fade show';
        document.body.appendChild(this.backdrop);
        
        this.backdrop.addEventListener('click', () => {
          this.hide();
        });
      }
    }

    hideBackdrop() {
      if (this.backdrop) {
        this.backdrop.remove();
        this.backdrop = null;
      }
    }
  }

  // 下拉菜单功能
  class Dropdown {
    constructor(element) {
      this.element = element;
      this.menu = element.nextElementSibling;
    }

    toggle() {
      if (this.menu.classList.contains('show')) {
        this.hide();
      } else {
        this.show();
      }
    }

    show() {
      this.menu.classList.add('show');
    }

    hide() {
      this.menu.classList.remove('show');
    }
  }

  // 工具提示功能
  class Tooltip {
    constructor(element) {
      this.element = element;
      this.tooltip = null;
    }

    show() {
      if (!this.tooltip) {
        this.tooltip = document.createElement('div');
        this.tooltip.className = 'tooltip fade show';
        this.tooltip.innerHTML = `<div class="tooltip-inner">${this.element.getAttribute('title')}</div>`;
        document.body.appendChild(this.tooltip);
      }
    }

    hide() {
      if (this.tooltip) {
        this.tooltip.remove();
        this.tooltip = null;
      }
    }
  }

  // 全局Bootstrap对象
  window.Bootstrap = {
    Modal: Modal,
    Dropdown: Dropdown,
    Tooltip: Tooltip
  };

  // 自动初始化
  document.addEventListener('DOMContentLoaded', function() {
    // 初始化模态框
    document.querySelectorAll('[data-bs-toggle="modal"]').forEach(function(trigger) {
      trigger.addEventListener('click', function(e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('data-bs-target'));
        if (target) {
          const modal = new Modal(target);
          modal.show();
        }
      });
    });

    // 初始化模态框关闭按钮
    document.querySelectorAll('[data-bs-dismiss="modal"]').forEach(function(closeBtn) {
      closeBtn.addEventListener('click', function() {
        const modal = this.closest('.modal');
        if (modal) {
          const modalInstance = new Modal(modal);
          modalInstance.hide();
        }
      });
    });

    // 初始化下拉菜单
    document.querySelectorAll('[data-bs-toggle="dropdown"]').forEach(function(trigger) {
      trigger.addEventListener('click', function(e) {
        e.preventDefault();
        const dropdown = new Dropdown(this);
        dropdown.toggle();
      });
    });

    // 初始化工具提示
    document.querySelectorAll('[data-bs-toggle="tooltip"]').forEach(function(element) {
      const tooltip = new Tooltip(element);
      
      element.addEventListener('mouseenter', function() {
        tooltip.show();
      });
      
      element.addEventListener('mouseleave', function() {
        tooltip.hide();
      });
    });
  });

  // 点击外部关闭下拉菜单
  document.addEventListener('click', function(e) {
    if (!e.target.matches('[data-bs-toggle="dropdown"]')) {
      document.querySelectorAll('.dropdown-menu.show').forEach(function(menu) {
        menu.classList.remove('show');
      });
    }
  });

})();

// 添加一些实用的工具函数
window.showAlert = function(message, type = 'info') {
  const alertDiv = document.createElement('div');
  alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
  alertDiv.innerHTML = `
    ${message}
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
  `;
  
  const container = document.querySelector('.container') || document.body;
  container.insertBefore(alertDiv, container.firstChild);
  
  // 自动关闭
  setTimeout(() => {
    alertDiv.remove();
  }, 5000);
};

window.showModal = function(modalId) {
  const modal = document.getElementById(modalId);
  if (modal) {
    const modalInstance = new Bootstrap.Modal(modal);
    modalInstance.show();
  }
};

window.hideModal = function(modalId) {
  const modal = document.getElementById(modalId);
  if (modal) {
    const modalInstance = new Bootstrap.Modal(modal);
    modalInstance.hide();
  }
};
