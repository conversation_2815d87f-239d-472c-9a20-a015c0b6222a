"""
知识库API处理器
处理知识库相关的HTTP请求
"""

import os
from typing import Dict, Any
from fastapi import HTTPException, Request
from fastapi.responses import HTMLResponse

from .services import knowledge_base_service
from .schemas import (
    DatasetResponse, CollectionListRequest, CollectionResponse,
    DataUpdateRequest, ApiResponse
)
from config.settings import settings
from config.templates import templates


async def get_datasets_list() -> ApiResponse:
    """获取数据集列表"""
    try:
        datasets_list = knowledge_base_service.get_datasets_list()
        return ApiResponse(code=200, data=datasets_list)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


async def get_collection_list_info(
    keyword: str = "",
    start_date: str = "",
    end_date: str = "",
    status: str = "",
    dataset: str = ""
) -> ApiResponse:
    """获取集合列表信息"""
    try:
        collections = knowledge_base_service.get_collection_list_info(
            keyword, start_date, end_date, status, dataset
        )
        return ApiResponse(
            code=200,
            data=collections,
            server_ip=settings.TEMPLATES_IP
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


async def get_same_name_files(cpxh: str, exclude_id: str) -> ApiResponse:
    """获取相同型号的文件"""
    try:
        files = knowledge_base_service.get_same_name_files(cpxh, exclude_id)
        return ApiResponse(code=200, data=files)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


async def get_dataset_datas(collection_id: str) -> ApiResponse:
    """获取数据集数据"""
    try:
        data = knowledge_base_service.get_dataset_datas(collection_id)
        return ApiResponse(code=200, data=data)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


async def index(request: Request):
    """主页处理器 - 使用Jinja2模板渲染"""
    try:
        # 确保全局数据已加载
        knowledge_base_service.get_global_data()

        # 准备模板上下文数据
        context = {
            "request": request,
            "title": "知识库管理系统",
            "server_ip": settings.TEMPLATES_IP,
            "api_ip": settings.API_IP
        }

        # 使用Jinja2模板渲染
        return templates.TemplateResponse("index.html", context)

    except Exception as e:
        print(f"✗ 模板渲染失败: {e}")
        # 如果模板渲染失败，返回错误页面
        error_html = f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>知识库管理系统 - 错误</title>
            <style>
                body {{ font-family: Arial, sans-serif; text-align: center; padding: 50px; }}
                .container {{ max-width: 600px; margin: 0 auto; }}
                .error {{ color: #F56C6C; }}
                .info {{ color: #409EFF; }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>知识库管理系统</h1>
                <p class="error">模板渲染失败</p>
                <p class="info">错误信息: {e}</p>
                <p>
                    <a href="javascript:location.reload()">重试</a>
                </p>
            </div>
        </body>
        </html>
        """
        return HTMLResponse(content=error_html, status_code=500)
